import os
import sys
from sqlalchemy.orm import sessionmaker

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from DataBase.DataBase import engine, Base
from Models.User.UserModel import User, UserRole
from Utils.HashPassword import Hash<PERSON>assword

# Create a new session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

# Hasher instance
hasher = HashPassword()

def create_admin_user():
    """Creates an admin user if one does not already exist."""
    # Check if an admin user already exists
    admin_user = db.query(User).filter(User.role == UserRole.ADMIN).first()
    if admin_user:
        print("Admin user already exists.")
        return

    # Define admin credentials
    admin_email = "<EMAIL>"
    admin_username = "admin"
    admin_password = "admin123"

    # Create the new admin user
    new_admin = User(
        username=admin_username,
        email=admin_email,
        hashed_password=hasher.create_hash(admin_password),
        first_name="Admin",
        last_name="User",
        role=UserRole.ADMIN,
        is_active=True
    )

    try:
        db.add(new_admin)
        db.commit()
        db.refresh(new_admin)
        print(f"Admin user '{new_admin.username}' created successfully.")
    except Exception as e:
        db.rollback()
        print(f"Error creating admin user: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("Seeding admin user...")
    # The following line is needed to create the tables if they don't exist.
    # You might manage this with Alembic or another migration tool in a real app.
    Base.metadata.create_all(bind=engine)
    create_admin_user()
