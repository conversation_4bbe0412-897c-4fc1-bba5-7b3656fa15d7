from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.OrderSchemas.OrderItemSchemas import OrderItem, OrderItemCreate, OrderItemUpdate
from DataBase.DataBase import get_db
from Models.Order.OrderItem import OrderItem as OrderItemModel

router = APIRouter(
    prefix="/order_items",
    tags=["order_items"],
)




@router.post("/", response_model=OrderItem)
def create_order_item_route(order_item: OrderItemCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[OrderItem])
def read_order_items_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{order_item_id}", response_model=OrderItem)
def read_order_item_route(order_item_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{order_item_id}", response_model=OrderItem)
def update_order_item_route(order_item_id: int, order_item: OrderItemUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{order_item_id}", response_model=OrderItem)
def delete_order_item_route(order_item_id: int, db: Session = Depends(get_db)):
    pass