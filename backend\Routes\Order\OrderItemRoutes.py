from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.OrderSchemas.OrderItemSchemas import OrderItem, OrderItemCreate, OrderItemUpdate
from DataBase.DataBase import get_db
from Models.Order.OrderItem import OrderItem as OrderItemModel

router = APIRouter(
    prefix="/order_items",
    tags=["order_items"],
)

# CRUD Operations for OrderItem
def get_order_item(db: Session, order_item_id: int) -> Optional[OrderItemModel]:
    return db.query(OrderItemModel).filter(OrderItemModel.id == order_item_id).first()

def get_order_items(db: Session, skip: int = 0, limit: int = 100) -> List[OrderItemModel]:
    return db.query(OrderItemModel).offset(skip).limit(limit).all()

def create_order_item(db: Session, order_item: OrderItemCreate) -> OrderItemModel:
    db_order_item = OrderItemModel(**order_item.dict())
    db.add(db_order_item)
    db.commit()
    db.refresh(db_order_item)
    return db_order_item

def update_order_item(db: Session, order_item_id: int, order_item: OrderItemUpdate) -> Optional[OrderItemModel]:
    db_order_item = db.query(OrderItemModel).filter(OrderItemModel.id == order_item_id).first()
    if db_order_item:
        update_data = order_item.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_order_item, key, value)
        db.commit()
        db.refresh(db_order_item)
    return db_order_item

def delete_order_item(db: Session, order_item_id: int) -> Optional[OrderItemModel]:
    db_order_item = db.query(OrderItemModel).filter(OrderItemModel.id == order_item_id).first()
    if db_order_item:
        db.delete(db_order_item)
        db.commit()
    return db_order_item


@router.post("/", response_model=OrderItem)
def create_order_item_route(order_item: OrderItemCreate, db: Session = Depends(get_db)):
    return create_order_item(db=db, order_item=order_item)

@router.get("/", response_model=List[OrderItem])
def read_order_items_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    order_items = get_order_items(db, skip=skip, limit=limit)
    return order_items

@router.get("/{order_item_id}", response_model=OrderItem)
def read_order_item_route(order_item_id: int, db: Session = Depends(get_db)):
    db_order_item = get_order_item(db, order_item_id=order_item_id)
    if db_order_item is None:
        raise HTTPException(status_code=404, detail="Order Item not found")
    return db_order_item

@router.put("/{order_item_id}", response_model=OrderItem)
def update_order_item_route(order_item_id: int, order_item: OrderItemUpdate, db: Session = Depends(get_db)):
    db_order_item = update_order_item(db, order_item_id=order_item_id, order_item=order_item)
    if db_order_item is None:
        raise HTTPException(status_code=404, detail="Order Item not found")
    return db_order_item

@router.delete("/{order_item_id}", response_model=OrderItem)
def delete_order_item_route(order_item_id: int, db: Session = Depends(get_db)):
    db_order_item = delete_order_item(db, order_item_id=order_item_id)
    if db_order_item is None:
        raise HTTPException(status_code=404, detail="Order Item not found")
    return db_order_item
