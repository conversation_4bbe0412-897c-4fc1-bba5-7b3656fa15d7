import {
  Dropdown,
  DropdownDivider,
  DropdownHeader,
  DropdownItem,
  Navbar,
  NavbarBrand,
  NavbarCollapse,
  NavbarLink,
  NavbarToggle,
  Avatar
} from "flowbite-react";
import { useNavigate } from "react-router-dom";
import Logo from "../../assets/logo.png"


const Header = () => {
    const navigate = useNavigate();

    const handleNavigation = (path: string) => {
        navigate(path);
    };

    const handleSignOut = () => {
        // Add your sign out logic here
        console.log("Signing out...");
        // For example: clear auth tokens, redirect to login, etc.
        navigate("/login");
    };

    return (
    <Navbar fluid rounded>
      <NavbarBrand onClick={() => handleNavigation("/")} className="cursor-pointer">
        <img src={Logo} className="mr-3 h-6 sm:h-9" alt="Flowbite React Logo" />
      </NavbarBrand>
      <div className="flex md:order-2">
        <Dropdown
          arrowIcon={false}
          inline
          label={
            <Avatar alt="User settings" img="https://flowbite.com/docs/images/people/profile-picture-5.jpg" rounded />
          }
        >
          <DropdownHeader>
            <span className="block text-sm">Bonnie Green</span>
            <span className="block truncate text-sm font-medium"><EMAIL></span>
          </DropdownHeader>
          <DropdownItem onClick={() => handleNavigation("/profile")}>
            Dashboard
          </DropdownItem>
          <DropdownItem onClick={() => handleNavigation("/settings")}>
            Settings
          </DropdownItem>
          <DropdownItem onClick={() => handleNavigation("/earnings")}>
            Earnings
          </DropdownItem>
          <DropdownDivider />
          <DropdownItem onClick={handleSignOut}>
            Sign out
          </DropdownItem>
        </Dropdown>
        <NavbarToggle />
      </div>
      <NavbarCollapse>
        <NavbarLink onClick={() => handleNavigation("/")} className="cursor-pointer" active>
          Home
        </NavbarLink>
        <NavbarLink onClick={() => handleNavigation("/about")} className="cursor-pointer">
          About
        </NavbarLink>
        <NavbarLink onClick={() => handleNavigation("/menus")} className="cursor-pointer">
          Menus
        </NavbarLink>
        <NavbarLink onClick={() => handleNavigation("/orders")} className="cursor-pointer">
          Orders
        </NavbarLink>
        <NavbarLink onClick={() => handleNavigation("/login")} className="cursor-pointer">
          Login
        </NavbarLink>
      </NavbarCollapse>
    </Navbar>

    );

}

export default Header