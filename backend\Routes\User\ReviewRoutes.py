from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.UserSchemas.ReviewSchemas import Review, ReviewCreate, ReviewUpdate
from DataBase.DataBase import get_db
from Models.User.ReviewModel import Review as ReviewModel

router = APIRouter(
    prefix="/reviews",
    tags=["reviews"],
)

# CRUD Operations for Review
def get_review(db: Session, review_id: int) -> Optional[ReviewModel]:
    return db.query(ReviewModel).filter(ReviewModel.id == review_id).first()

def get_reviews(db: Session, skip: int = 0, limit: int = 100) -> List[ReviewModel]:
    return db.query(ReviewModel).offset(skip).limit(limit).all()

def create_review(db: Session, review: ReviewCreate) -> ReviewModel:
    db_review = ReviewModel(**review.dict())
    db.add(db_review)
    db.commit()
    db.refresh(db_review)
    return db_review

def update_review(db: Session, review_id: int, review: ReviewUpdate) -> Optional[ReviewModel]:
    db_review = db.query(ReviewModel).filter(ReviewModel.id == review_id).first()
    if db_review:
        update_data = review.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_review, key, value)
        db.commit()
        db.refresh(db_review)
    return db_review

def delete_review(db: Session, review_id: int) -> Optional[ReviewModel]:
    db_review = db.query(ReviewModel).filter(ReviewModel.id == review_id).first()
    if db_review:
        db.delete(db_review)
        db.commit()
    return db_review


@router.post("/", response_model=Review)
def create_review_route(review: ReviewCreate, db: Session = Depends(get_db)):
    return create_review(db=db, review=review)

@router.get("/", response_model=List[Review])
def read_reviews_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    reviews = get_reviews(db, skip=skip, limit=limit)
    return reviews

@router.get("/{review_id}", response_model=Review)
def read_review_route(review_id: int, db: Session = Depends(get_db)):
    db_review = get_review(db, review_id=review_id)
    if db_review is None:
        raise HTTPException(status_code=404, detail="Review not found")
    return db_review

@router.put("/{review_id}", response_model=Review)
def update_review_route(review_id: int, review: ReviewUpdate, db: Session = Depends(get_db)):
    db_review = update_review(db, review_id=review_id, review=review)
    if db_review is None:
        raise HTTPException(status_code=404, detail="Review not found")
    return db_review

@router.delete("/{review_id}", response_model=Review)
def delete_review_route(review_id: int, db: Session = Depends(get_db)):
    db_review = delete_review(db, review_id=review_id)
    if db_review is None:
        raise HTTPException(status_code=404, detail="Review not found")
    return db_review
