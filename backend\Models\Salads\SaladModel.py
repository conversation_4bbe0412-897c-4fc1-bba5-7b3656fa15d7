from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON>an, ForeignKey, Table
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

# Association Table for Salad and Ingredient
salad_ingredients = Table('salad_ingredients', Base.metadata,
    Column('salad_id', Integer, ForeignKey('salads.id'), primary_key=True),
    Column('ingredient_id', Integer, ForeignKey('ingredients.id'), primary_key=True)
)

class Salad(Base):
    __tablename__ = 'salads'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255))
    price = Column(Float, nullable=False)
    dressing = Column(String(100))
    is_vegetarian = Column(Boolean, default=False)
    image_url = Column(String(255))
    is_available = Column(Boolean, default=True)

    category_id = Column(Integer, ForeignKey('categories.id'))
    category = relationship("Category", back_populates="salads")

    menu_item_id = Column(Integer, ForeignKey('menu_items.id'))
    menu_item = relationship("MenuItem", back_populates="salad")

    # Many-to-many relationship with Ingredient
    ingredients = relationship("Ingredient", secondary=salad_ingredients, back_populates="salads")

    def __repr__(self):
        return f"<Salad(id={self.id}, name='{self.name}')>"
