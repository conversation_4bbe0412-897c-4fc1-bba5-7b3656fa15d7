from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.MenuSchemas.MenuSchemas import Menu, MenuCreate, MenuUpdate
from DataBase.DataBase import get_db
from Models.Menu.MenuModel import Menu as MenuModel

router = APIRouter(
    prefix="/menus",
    tags=["menus"],
)



def delete_menu(db: Session, menu_id: int) -> Optional[MenuModel]:
    pass


@router.post("/", response_model=Menu)
def create_menu_route(menu: MenuCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Menu])
def read_menus_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{menu_id}", response_model=Menu)
def read_menu_route(menu_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{menu_id}", response_model=Menu)
def update_menu_route(menu_id: int, menu: MenuUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{menu_id}", response_model=Menu)
def delete_menu_route(menu_id: int, db: Session = Depends(get_db)):
    pass