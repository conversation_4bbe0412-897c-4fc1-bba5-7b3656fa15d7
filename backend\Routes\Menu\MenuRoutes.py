from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.MenuSchemas.MenuSchemas import Menu, MenuCreate, MenuUpdate
from DataBase.DataBase import get_db
from Models.Menu.MenuModel import Menu as MenuModel

router = APIRouter(
    prefix="/menus",
    tags=["menus"],
)

# CRUD Operations for Menu
def get_menu(db: Session, menu_id: int) -> Optional[MenuModel]:
    return db.query(MenuModel).filter(MenuModel.id == menu_id).first()

def get_menus(db: Session, skip: int = 0, limit: int = 100) -> List[MenuModel]:
    return db.query(MenuModel).offset(skip).limit(limit).all()

def create_menu(db: Session, menu: MenuCreate) -> MenuModel:
    db_menu = MenuModel(**menu.dict())
    db.add(db_menu)
    db.commit()
    db.refresh(db_menu)
    return db_menu

def update_menu(db: Session, menu_id: int, menu: MenuUpdate) -> Optional[MenuModel]:
    db_menu = db.query(MenuModel).filter(MenuModel.id == menu_id).first()
    if db_menu:
        update_data = menu.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_menu, key, value)
        db.commit()
        db.refresh(db_menu)
    return db_menu

def delete_menu(db: Session, menu_id: int) -> Optional[MenuModel]:
    db_menu = db.query(MenuModel).filter(MenuModel.id == menu_id).first()
    if db_menu:
        db.delete(db_menu)
        db.commit()
    return db_menu


@router.post("/", response_model=Menu)
def create_menu_route(menu: MenuCreate, db: Session = Depends(get_db)):
    return create_menu(db=db, menu=menu)

@router.get("/", response_model=List[Menu])
def read_menus_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    menus = get_menus(db, skip=skip, limit=limit)
    return menus

@router.get("/{menu_id}", response_model=Menu)
def read_menu_route(menu_id: int, db: Session = Depends(get_db)):
    db_menu = get_menu(db, menu_id=menu_id)
    if db_menu is None:
        raise HTTPException(status_code=404, detail="Menu not found")
    return db_menu

@router.put("/{menu_id}", response_model=Menu)
def update_menu_route(menu_id: int, menu: MenuUpdate, db: Session = Depends(get_db)):
    db_menu = update_menu(db, menu_id=menu_id, menu=menu)
    if db_menu is None:
        raise HTTPException(status_code=404, detail="Menu not found")
    return db_menu

@router.delete("/{menu_id}", response_model=Menu)
def delete_menu_route(menu_id: int, db: Session = Depends(get_db)):
    db_menu = delete_menu(db, menu_id=menu_id)
    if db_menu is None:
        raise HTTPException(status_code=404, detail="Menu not found")
    return db_menu
