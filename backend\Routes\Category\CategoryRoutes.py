from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.CategorySchemas.CategorySchemas import Category, CategoryCreate, CategoryUpdate
from DataBase.DataBase import get_db
from Models.Category.CategoryModel import Category as CategoryModel

router = APIRouter(
    prefix="/categories",
    tags=["categories"],
)

# CRUD Operations for Category
def get_category(db: Session, category_id: int) -> Optional[CategoryModel]:
    return db.query(CategoryModel).filter(CategoryModel.id == category_id).first()

def get_categories(db: Session, skip: int = 0, limit: int = 100) -> List[CategoryModel]:
    return db.query(CategoryModel).offset(skip).limit(limit).all()

def create_category(db: Session, category: CategoryCreate) -> CategoryModel:
    db_category = CategoryModel(**category.dict())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category

def update_category(db: Session, category_id: int, category: CategoryUpdate) -> Optional[CategoryModel]:
    db_category = db.query(CategoryModel).filter(CategoryModel.id == category_id).first()
    if db_category:
        update_data = category.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_category, key, value)
        db.commit()
        db.refresh(db_category)
    return db_category

def delete_category(db: Session, category_id: int) -> Optional[CategoryModel]:
    db_category = db.query(CategoryModel).filter(CategoryModel.id == category_id).first()
    if db_category:
        db.delete(db_category)
        db.commit()
    return db_category


@router.post("/", response_model=Category)
def create_category_route(category: CategoryCreate, db: Session = Depends(get_db)):
    return create_category(db=db, category=category)

@router.get("/", response_model=List[Category])
def read_categories_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    categories = get_categories(db, skip=skip, limit=limit)
    return categories

@router.get("/{category_id}", response_model=Category)
def read_category_route(category_id: int, db: Session = Depends(get_db)):
    db_category = get_category(db, category_id=category_id)
    if db_category is None:
        raise HTTPException(status_code=404, detail="Category not found")
    return db_category

@router.put("/{category_id}", response_model=Category)
def update_category_route(category_id: int, category: CategoryUpdate, db: Session = Depends(get_db)):
    db_category = update_category(db, category_id=category_id, category=category)
    if db_category is None:
        raise HTTPException(status_code=404, detail="Category not found")
    return db_category

@router.delete("/{category_id}", response_model=Category)
def delete_category_route(category_id: int, db: Session = Depends(get_db)):
    db_category = delete_category(db, category_id=category_id)
    if db_category is None:
        raise HTTPException(status_code=404, detail="Category not found")
    return db_category
