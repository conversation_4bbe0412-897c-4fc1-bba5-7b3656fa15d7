from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.CategorySchemas.CategorySchemas import Category, CategoryCreate, CategoryUpdate
from DataBase.DataBase import get_db
from Models.Category.CategoryModel import Category as CategoryModel

router = APIRouter(
    prefix="/categories",
    tags=["categories"],
)


@router.post("/", response_model=Category)
def create_category_route(category: CategoryCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Category])
def read_categories_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{category_id}", response_model=Category)
def read_category_route(category_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{category_id}", response_model=Category)
def update_category_route(category_id: int, category: CategoryUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{category_id}", response_model=Category)
def delete_category_route(category_id: int, db: Session = Depends(get_db)):
    pass