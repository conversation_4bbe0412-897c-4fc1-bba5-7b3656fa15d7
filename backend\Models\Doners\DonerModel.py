from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON>an, Foreign<PERSON>ey, Table
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

# Association Table for Doner and Ingredient
doner_ingredients = Table('doner_ingredients', Base.metadata,
    Column('doner_id', Integer, ForeignKey('doners.id'), primary_key=True),
    Column('ingredient_id', Integer, ForeignKey('ingredients.id'), primary_key=True)
)

class Doner(Base):
    __tablename__ = 'doners'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255))
    price = Column(Float, nullable=False)
    meat_type = Column(String(50))
    style = Column(String(50)) # e.g., Wrap, Plate
    image_url = Column(String(255))
    is_available = Column(Boolean, default=True)

    category_id = Column(Integer, ForeignKey('categories.id'))
    category = relationship("Category", back_populates="doners")

    menu_item_id = Column(Integer, ForeignKey('menu_items.id'))
    menu_item = relationship("MenuItem", back_populates="doner")

    # Many-to-many relationship with Ingredient
    ingredients = relationship("Ingredient", secondary=doner_ingredients, back_populates="doners")

    def __repr__(self):
        return f"<Doner(id={self.id}, name='{self.name}')>"
