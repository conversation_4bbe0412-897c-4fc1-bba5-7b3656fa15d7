from ...DataBase.DataBase import get_db
from ...Models.User.UserModel import User, UserRole
from ...Schemas.UserSchemas.UserSchemas import UserCreate, UserLogin, User, UserUpdate, Token
from ...Utils.HashPassword import HashPassword
from ...Utils.JWT import <PERSON>th<PERSON><PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List


hasher = HashPassword()
auth_handler = AuthHandler()

class UserControllers:
    pass