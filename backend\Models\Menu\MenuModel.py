from sqlalchemy import Column, Integer, String, Float, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

class Menu(Base):
    __tablename__ = 'menus'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255))
    price = Column(Float, nullable=False)

    # Foreign Keys to specific menu items
    drink_id = Column(Integer, ForeignKey('menu_items.id'), nullable=False)
    salad_id = Column(Integer, ForeignKey('menu_items.id'), nullable=False)
    main_course_id = Column(Integer, ForeignKey('menu_items.id'), nullable=False)

    # Relationships to the MenuItem model
    drink = relationship("MenuItem", foreign_keys=[drink_id])
    salad = relationship("MenuItem", foreign_keys=[salad_id])
    main_course = relationship("MenuItem", foreign_keys=[main_course_id])

    # Relationship to order items
    order_items = relationship("OrderItem", back_populates="menu")

    def __repr__(self):
        return f"<Menu(id={self.id}, name='{self.name}')>"
