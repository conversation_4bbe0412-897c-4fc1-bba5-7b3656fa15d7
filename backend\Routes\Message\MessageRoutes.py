from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.MessageSchemas.MessageSchemas import Message, MessageCreate, MessageUpdate
from DataBase.DataBase import get_db
from Models.Message.MessageModel import Message as MessageModel

router = APIRouter(
    prefix="/messages",
    tags=["messages"],
)

# CRUD Operations for Message
def get_message(db: Session, message_id: int) -> Optional[MessageModel]:
    return db.query(MessageModel).filter(MessageModel.id == message_id).first()

def get_messages(db: Session, skip: int = 0, limit: int = 100) -> List[MessageModel]:
    return db.query(MessageModel).offset(skip).limit(limit).all()

def create_message(db: Session, message: MessageCreate) -> MessageModel:
    db_message = MessageModel(**message.dict())
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    return db_message

def update_message(db: Session, message_id: int, message: MessageUpdate) -> Optional[MessageModel]:
    db_message = db.query(MessageModel).filter(MessageModel.id == message_id).first()
    if db_message:
        update_data = message.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_message, key, value)
        db.commit()
        db.refresh(db_message)
    return db_message

def delete_message(db: Session, message_id: int) -> Optional[MessageModel]:
    db_message = db.query(MessageModel).filter(MessageModel.id == message_id).first()
    if db_message:
        db.delete(db_message)
        db.commit()
    return db_message


@router.post("/", response_model=Message)
def create_message_route(message: MessageCreate, db: Session = Depends(get_db)):
    return create_message(db=db, message=message)

@router.get("/", response_model=List[Message])
def read_messages_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    messages = get_messages(db, skip=skip, limit=limit)
    return messages

@router.get("/{message_id}", response_model=Message)
def read_message_route(message_id: int, db: Session = Depends(get_db)):
    db_message = get_message(db, message_id=message_id)
    if db_message is None:
        raise HTTPException(status_code=404, detail="Message not found")
    return db_message

@router.put("/{message_id}", response_model=Message)
def update_message_route(message_id: int, message: MessageUpdate, db: Session = Depends(get_db)):
    db_message = update_message(db, message_id=message_id, message=message)
    if db_message is None:
        raise HTTPException(status_code=404, detail="Message not found")
    return db_message

@router.delete("/{message_id}", response_model=Message)
def delete_message_route(message_id: int, db: Session = Depends(get_db)):
    db_message = delete_message(db, message_id=message_id)
    if db_message is None:
        raise HTTPException(status_code=404, detail="Message not found")
    return db_message
