from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.MessageSchemas.MessageSchemas import Message, MessageCreate, MessageUpdate
from DataBase.DataBase import get_db
from Models.Message.MessageModel import Message as MessageModel

router = APIRouter(
    prefix="/messages",
    tags=["messages"],
)



@router.post("/", response_model=Message)
def create_message_route(message: MessageCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Message])
def read_messages_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{message_id}", response_model=Message)
def read_message_route(message_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{message_id}", response_model=Message)
def update_message_route(message_id: int, message: MessageUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{message_id}", response_model=Message)
def delete_message_route(message_id: int, db: Session = Depends(get_db)):
    pass