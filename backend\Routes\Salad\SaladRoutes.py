from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.SaladSchemas.SaladSchemas import Salad, SaladCreate, SaladUpdate
from DataBase.DataBase import get_db
from Models.Salads.SaladModel import Salad as SaladModel

router = APIRouter(
    prefix="/salads",
    tags=["salads"],
)

# CRUD Operations for Salad
def get_salad(db: Session, salad_id: int) -> Optional[SaladModel]:
    return db.query(SaladModel).filter(SaladModel.id == salad_id).first()

def get_salads(db: Session, skip: int = 0, limit: int = 100) -> List[SaladModel]:
    return db.query(SaladModel).offset(skip).limit(limit).all()

def create_salad(db: Session, salad: SaladCreate) -> SaladModel:
    db_salad = SaladModel(**salad.dict())
    db.add(db_salad)
    db.commit()
    db.refresh(db_salad)
    return db_salad

def update_salad(db: Session, salad_id: int, salad: SaladUpdate) -> Optional[SaladModel]:
    db_salad = db.query(SaladModel).filter(SaladModel.id == salad_id).first()
    if db_salad:
        update_data = salad.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_salad, key, value)
        db.commit()
        db.refresh(db_salad)
    return db_salad

def delete_salad(db: Session, salad_id: int) -> Optional[SaladModel]:
    db_salad = db.query(SaladModel).filter(SaladModel.id == salad_id).first()
    if db_salad:
        db.delete(db_salad)
        db.commit()
    return db_salad


@router.post("/", response_model=Salad)
def create_salad_route(salad: SaladCreate, db: Session = Depends(get_db)):
    return create_salad(db=db, salad=salad)

@router.get("/", response_model=List[Salad])
def read_salads_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    salads = get_salads(db, skip=skip, limit=limit)
    return salads

@router.get("/{salad_id}", response_model=Salad)
def read_salad_route(salad_id: int, db: Session = Depends(get_db)):
    db_salad = get_salad(db, salad_id=salad_id)
    if db_salad is None:
        raise HTTPException(status_code=404, detail="Salad not found")
    return db_salad

@router.put("/{salad_id}", response_model=Salad)
def update_salad_route(salad_id: int, salad: SaladUpdate, db: Session = Depends(get_db)):
    db_salad = update_salad(db, salad_id=salad_id, salad=salad)
    if db_salad is None:
        raise HTTPException(status_code=404, detail="Salad not found")
    return db_salad

@router.delete("/{salad_id}", response_model=Salad)
def delete_salad_route(salad_id: int, db: Session = Depends(get_db)):
    db_salad = delete_salad(db, salad_id=salad_id)
    if db_salad is None:
        raise HTTPException(status_code=404, detail="Salad not found")
    return db_salad
