from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.SaladSchemas.SaladSchemas import Salad, SaladCreate, SaladUpdate
from DataBase.DataBase import get_db
from Models.Salads.SaladModel import Salad as SaladModel

router = APIRouter(
    prefix="/salads",
    tags=["salads"],
)




@router.post("/", response_model=Salad)
def create_salad_route(salad: SaladCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Salad])
def read_salads_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{salad_id}", response_model=Salad)
def read_salad_route(salad_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{salad_id}", response_model=Salad)
def update_salad_route(salad_id: int, salad: SaladUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{salad_id}", response_model=Salad)
def delete_salad_route(salad_id: int, db: Session = Depends(get_db)):
    pass