from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Menu.MenuModel import Menu as MenuModel
from ...Schemas.MenuSchemas.MenuSchemas import <PERSON>u, MenuCreate, MenuUpdate


class MenuControllers:
    
    @staticmethod
    async def get_menu(db: Session, menu_id: int) -> MenuModel:
        """Get a single menu by ID"""
        menu = db.query(MenuModel).filter(MenuModel.id == menu_id).first()
        if not menu:
            raise HTTPException(status_code=404, detail="Menu not found")
        return menu
    
    @staticmethod
    async def get_menus(db: Session, skip: int = 0, limit: int = 100) -> List[MenuModel]:
        """Get all menus with pagination"""
        return db.query(MenuModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_menu(db: Session, menu: MenuCreate) -> MenuModel:
        """Create a new menu"""
        db_menu = MenuModel(**menu.model_dump())
        db.add(db_menu)
        db.commit()
        db.refresh(db_menu)
        return db_menu
    
    @staticmethod
    async def update_menu(db: Session, menu_id: int, menu: MenuUpdate) -> MenuModel:
        """Update an existing menu"""
        db_menu = db.query(MenuModel).filter(MenuModel.id == menu_id).first()
        if not db_menu:
            raise HTTPException(status_code=404, detail="Menu not found")
        
        update_data = menu.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_menu, key, value)
        
        db.commit()
        db.refresh(db_menu)
        return db_menu
    
    @staticmethod
    async def delete_menu(db: Session, menu_id: int) -> MenuModel:
        """Delete a menu"""
        db_menu = db.query(MenuModel).filter(MenuModel.id == menu_id).first()
        if not db_menu:
            raise HTTPException(status_code=404, detail="Menu not found")
        
        db.delete(db_menu)
        db.commit()
        return db_menu
