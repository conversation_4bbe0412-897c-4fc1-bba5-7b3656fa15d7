from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.IngredientSchemas.IngredientSchemas import Ingredient, IngredientCreate, IngredientUpdate
from DataBase.DataBase import get_db
from Models.Ingredients.IngredientsModel import Ingredient as IngredientModel

router = APIRouter(
    prefix="/ingredients",
    tags=["ingredients"],
)

# CRUD Operations for Ingredient
def get_ingredient(db: Session, ingredient_id: int) -> Optional[IngredientModel]:
    return db.query(IngredientModel).filter(IngredientModel.id == ingredient_id).first()

def get_ingredients(db: Session, skip: int = 0, limit: int = 100) -> List[IngredientModel]:
    return db.query(IngredientModel).offset(skip).limit(limit).all()

def create_ingredient(db: Session, ingredient: IngredientCreate) -> IngredientModel:
    db_ingredient = IngredientModel(**ingredient.dict())
    db.add(db_ingredient)
    db.commit()
    db.refresh(db_ingredient)
    return db_ingredient

def update_ingredient(db: Session, ingredient_id: int, ingredient: IngredientUpdate) -> Optional[IngredientModel]:
    db_ingredient = db.query(IngredientModel).filter(IngredientModel.id == ingredient_id).first()
    if db_ingredient:
        update_data = ingredient.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_ingredient, key, value)
        db.commit()
        db.refresh(db_ingredient)
    return db_ingredient

def delete_ingredient(db: Session, ingredient_id: int) -> Optional[IngredientModel]:
    db_ingredient = db.query(IngredientModel).filter(IngredientModel.id == ingredient_id).first()
    if db_ingredient:
        db.delete(db_ingredient)
        db.commit()
    return db_ingredient


@router.post("/", response_model=Ingredient)
def create_ingredient_route(ingredient: IngredientCreate, db: Session = Depends(get_db)):
    return create_ingredient(db=db, ingredient=ingredient)

@router.get("/", response_model=List[Ingredient])
def read_ingredients_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    ingredients = get_ingredients(db, skip=skip, limit=limit)
    return ingredients

@router.get("/{ingredient_id}", response_model=Ingredient)
def read_ingredient_route(ingredient_id: int, db: Session = Depends(get_db)):
    db_ingredient = get_ingredient(db, ingredient_id=ingredient_id)
    if db_ingredient is None:
        raise HTTPException(status_code=404, detail="Ingredient not found")
    return db_ingredient

@router.put("/{ingredient_id}", response_model=Ingredient)
def update_ingredient_route(ingredient_id: int, ingredient: IngredientUpdate, db: Session = Depends(get_db)):
    db_ingredient = update_ingredient(db, ingredient_id=ingredient_id, ingredient=ingredient)
    if db_ingredient is None:
        raise HTTPException(status_code=404, detail="Ingredient not found")
    return db_ingredient

@router.delete("/{ingredient_id}", response_model=Ingredient)
def delete_ingredient_route(ingredient_id: int, db: Session = Depends(get_db)):
    db_ingredient = delete_ingredient(db, ingredient_id=ingredient_id)
    if db_ingredient is None:
        raise HTTPException(status_code=404, detail="Ingredient not found")
    return db_ingredient

