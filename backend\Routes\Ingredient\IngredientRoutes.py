from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.IngredientSchemas.IngredientSchemas import Ingredient, IngredientCreate, IngredientUpdate
from DataBase.DataBase import get_db
from Models.Ingredients.IngredientsModel import Ingredient as IngredientModel

router = APIRouter(
    prefix="/ingredients",
    tags=["ingredients"],
)



@router.post("/", response_model=Ingredient)
def create_ingredient_route(ingredient: IngredientCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Ingredient])
def read_ingredients_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{ingredient_id}", response_model=Ingredient)
def read_ingredient_route(ingredient_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{ingredient_id}", response_model=Ingredient)
def update_ingredient_route(ingredient_id: int, ingredient: IngredientUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{ingredient_id}", response_model=Ingredient)
def delete_ingredient_route(ingredient_id: int, db: Session = Depends(get_db)):
    pass