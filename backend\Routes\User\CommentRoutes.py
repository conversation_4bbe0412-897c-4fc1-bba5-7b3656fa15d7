from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.UserSchemas.CommentSchemas import Comment, CommentCreate, CommentUpdate
from DataBase.DataBase import get_db
from Models.User.CommentModel import Comment as CommentModel

router = APIRouter(
    prefix="/comments",
    tags=["comments"],
)

# CRUD Operations for Comment
def get_comment(db: Session, comment_id: int) -> Optional[CommentModel]:
    return db.query(CommentModel).filter(CommentModel.id == comment_id).first()

def get_comments(db: Session, skip: int = 0, limit: int = 100) -> List[CommentModel]:
    return db.query(CommentModel).offset(skip).limit(limit).all()

def create_comment(db: Session, comment: CommentCreate) -> CommentModel:
    db_comment = CommentModel(**comment.dict())
    db.add(db_comment)
    db.commit()
    db.refresh(db_comment)
    return db_comment

def update_comment(db: Session, comment_id: int, comment: CommentUpdate) -> Optional[CommentModel]:
    db_comment = db.query(CommentModel).filter(CommentModel.id == comment_id).first()
    if db_comment:
        update_data = comment.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_comment, key, value)
        db.commit()
        db.refresh(db_comment)
    return db_comment

def delete_comment(db: Session, comment_id: int) -> Optional[CommentModel]:
    db_comment = db.query(CommentModel).filter(CommentModel.id == comment_id).first()
    if db_comment:
        db.delete(db_comment)
        db.commit()
    return db_comment


@router.post("/", response_model=Comment)
def create_comment_route(comment: CommentCreate, db: Session = Depends(get_db)):
    return create_comment(db=db, comment=comment)

@router.get("/", response_model=List[Comment])
def read_comments_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    comments = get_comments(db, skip=skip, limit=limit)
    return comments

@router.get("/{comment_id}", response_model=Comment)
def read_comment_route(comment_id: int, db: Session = Depends(get_db)):
    db_comment = get_comment(db, comment_id=comment_id)
    if db_comment is None:
        raise HTTPException(status_code=404, detail="Comment not found")
    return db_comment

@router.put("/{comment_id}", response_model=Comment)
def update_comment_route(comment_id: int, comment: CommentUpdate, db: Session = Depends(get_db)):
    db_comment = update_comment(db, comment_id=comment_id, comment=comment)
    if db_comment is None:
        raise HTTPException(status_code=404, detail="Comment not found")
    return db_comment

@router.delete("/{comment_id}", response_model=Comment)
def delete_comment_route(comment_id: int, db: Session = Depends(get_db)):
    db_comment = delete_comment(db, comment_id=comment_id)
    if db_comment is None:
        raise HTTPException(status_code=404, detail="Comment not found")
    return db_comment
