from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.OrderSchemas.OrderSchemas import Order, OrderCreate, OrderUpdate
from DataBase.DataBase import get_db
from Models.Order.OrderModel import Order as OrderModel

router = APIRouter(
    prefix="/orders",
    tags=["orders"],
)



@router.post("/", response_model=Order)
def create_order_route(order: OrderCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Order])
def read_orders_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{order_id}", response_model=Order)
def read_order_route(order_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{order_id}", response_model=Order)
def update_order_route(order_id: int, order: OrderUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{order_id}", response_model=Order)
def delete_order_route(order_id: int, db: Session = Depends(get_db)):
    pass