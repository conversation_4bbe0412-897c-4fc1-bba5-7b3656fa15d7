from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.OrderSchemas.OrderSchemas import Order, OrderCreate, OrderUpdate
from DataBase.DataBase import get_db
from Models.Order.OrderModel import Order as OrderModel

router = APIRouter(
    prefix="/orders",
    tags=["orders"],
)

# CRUD Operations for Order
def get_order(db: Session, order_id: int) -> Optional[OrderModel]:
    return db.query(OrderModel).filter(OrderModel.id == order_id).first()

def get_orders(db: Session, skip: int = 0, limit: int = 100) -> List[OrderModel]:
    return db.query(OrderModel).offset(skip).limit(limit).all()

def create_order(db: Session, order: OrderCreate) -> OrderModel:
    db_order = OrderModel(**order.dict())
    db.add(db_order)
    db.commit()
    db.refresh(db_order)
    return db_order

def update_order(db: Session, order_id: int, order: OrderUpdate) -> Optional[OrderModel]:
    db_order = db.query(OrderModel).filter(OrderModel.id == order_id).first()
    if db_order:
        update_data = order.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_order, key, value)
        db.commit()
        db.refresh(db_order)
    return db_order

def delete_order(db: Session, order_id: int) -> Optional[OrderModel]:
    db_order = db.query(OrderModel).filter(OrderModel.id == order_id).first()
    if db_order:
        db.delete(db_order)
        db.commit()
    return db_order


@router.post("/", response_model=Order)
def create_order_route(order: OrderCreate, db: Session = Depends(get_db)):
    return create_order(db=db, order=order)

@router.get("/", response_model=List[Order])
def read_orders_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    orders = get_orders(db, skip=skip, limit=limit)
    return orders

@router.get("/{order_id}", response_model=Order)
def read_order_route(order_id: int, db: Session = Depends(get_db)):
    db_order = get_order(db, order_id=order_id)
    if db_order is None:
        raise HTTPException(status_code=404, detail="Order not found")
    return db_order

@router.put("/{order_id}", response_model=Order)
def update_order_route(order_id: int, order: OrderUpdate, db: Session = Depends(get_db)):
    db_order = update_order(db, order_id=order_id, order=order)
    if db_order is None:
        raise HTTPException(status_code=404, detail="Order not found")
    return db_order

@router.delete("/{order_id}", response_model=Order)
def delete_order_route(order_id: int, db: Session = Depends(get_db)):
    db_order = delete_order(db, order_id=order_id)
    if db_order is None:
        raise HTTPException(status_code=404, detail="Order not found")
    return db_order
