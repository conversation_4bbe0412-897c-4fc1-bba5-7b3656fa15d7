from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.DonerSchemas.DonerSchemas import Doner, DonerCreate, DonerUpdate
from DataBase.DataBase import get_db
from Models.Doners.DonerModel import Doner as DonerModel

router = APIRouter(
    prefix="/doners",
    tags=["doners"],
)

@router.post("/", response_model=Doner)
def create_doner_route(doner: DonerCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Doner])
def read_doners_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{doner_id}", response_model=Doner)
def read_doner_route(doner_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{doner_id}", response_model=Doner)
def update_doner_route(doner_id: int, doner: DonerUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{doner_id}", response_model=Doner)
def delete_doner_route(doner_id: int, db: Session = Depends(get_db)):
    pass