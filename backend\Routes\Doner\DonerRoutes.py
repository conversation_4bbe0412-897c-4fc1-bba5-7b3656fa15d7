from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.DonerSchemas.DonerSchemas import Doner, DonerCreate, DonerUpdate
from DataBase.DataBase import get_db
from Models.Doners.DonerModel import Doner as DonerModel

router = APIRouter(
    prefix="/doners",
    tags=["doners"],
)

# CRUD Operations for Doner
def get_doner(db: Session, doner_id: int) -> Optional[DonerModel]:
    return db.query(DonerModel).filter(DonerModel.id == doner_id).first()

def get_doners(db: Session, skip: int = 0, limit: int = 100) -> List[DonerModel]:
    return db.query(DonerModel).offset(skip).limit(limit).all()

def create_doner(db: Session, doner: DonerCreate) -> DonerModel:
    db_doner = DonerModel(**doner.dict())
    db.add(db_doner)
    db.commit()
    db.refresh(db_doner)
    return db_doner

def update_doner(db: Session, doner_id: int, doner: DonerUpdate) -> Optional[DonerModel]:
    db_doner = db.query(DonerModel).filter(DonerModel.id == doner_id).first()
    if db_doner:
        update_data = doner.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_doner, key, value)
        db.commit()
        db.refresh(db_doner)
    return db_doner

def delete_doner(db: Session, doner_id: int) -> Optional[DonerModel]:
    db_doner = db.query(DonerModel).filter(DonerModel.id == doner_id).first()
    if db_doner:
        db.delete(db_doner)
        db.commit()
    return db_doner


@router.post("/", response_model=Doner)
def create_doner_route(doner: DonerCreate, db: Session = Depends(get_db)):
    return create_doner(db=db, doner=doner)

@router.get("/", response_model=List[Doner])
def read_doners_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    doners = get_doners(db, skip=skip, limit=limit)
    return doners

@router.get("/{doner_id}", response_model=Doner)
def read_doner_route(doner_id: int, db: Session = Depends(get_db)):
    db_doner = get_doner(db, doner_id=doner_id)
    if db_doner is None:
        raise HTTPException(status_code=404, detail="Doner not found")
    return db_doner

@router.put("/{doner_id}", response_model=Doner)
def update_doner_route(doner_id: int, doner: DonerUpdate, db: Session = Depends(get_db)):
    db_doner = update_doner(db, doner_id=doner_id, doner=doner)
    if db_doner is None:
        raise HTTPException(status_code=404, detail="Doner not found")
    return db_doner

@router.delete("/{doner_id}", response_model=Doner)
def delete_doner_route(doner_id: int, db: Session = Depends(get_db)):
    db_doner = delete_doner(db, doner_id=doner_id)
    if db_doner is None:
        raise HTTPException(status_code=404, detail="Doner not found")
    return db_doner
