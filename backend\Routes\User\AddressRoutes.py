from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.UserSchemas.AddressSchemas import Address, AddressCreate, AddressUpdate
from DataBase.DataBase import get_db
from Models.User.AddressModel import Address as AddressModel

router = APIRouter(
    prefix="/addresses",
    tags=["addresses"],
)

# CRUD Operations for Address
def get_address(db: Session, address_id: int) -> Optional[AddressModel]:
    return db.query(AddressModel).filter(AddressModel.id == address_id).first()

def get_addresses(db: Session, skip: int = 0, limit: int = 100) -> List[AddressModel]:
    return db.query(AddressModel).offset(skip).limit(limit).all()

def create_address(db: Session, address: AddressCreate) -> AddressModel:
    db_address = AddressModel(**address.dict())
    db.add(db_address)
    db.commit()
    db.refresh(db_address)
    return db_address

def update_address(db: Session, address_id: int, address: AddressUpdate) -> Optional[AddressModel]:
    db_address = db.query(AddressModel).filter(AddressModel.id == address_id).first()
    if db_address:
        update_data = address.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_address, key, value)
        db.commit()
        db.refresh(db_address)
    return db_address

def delete_address(db: Session, address_id: int) -> Optional[AddressModel]:
    db_address = db.query(AddressModel).filter(AddressModel.id == address_id).first()
    if db_address:
        db.delete(db_address)
        db.commit()
    return db_address


@router.post("/", response_model=Address)
def create_address_route(address: AddressCreate, db: Session = Depends(get_db)):
    return create_address(db=db, address=address)

@router.get("/", response_model=List[Address])
def read_addresses_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    addresses = get_addresses(db, skip=skip, limit=limit)
    return addresses

@router.get("/{address_id}", response_model=Address)
def read_address_route(address_id: int, db: Session = Depends(get_db)):
    db_address = get_address(db, address_id=address_id)
    if db_address is None:
        raise HTTPException(status_code=404, detail="Address not found")
    return db_address

@router.put("/{address_id}", response_model=Address)
def update_address_route(address_id: int, address: AddressUpdate, db: Session = Depends(get_db)):
    db_address = update_address(db, address_id=address_id, address=address)
    if db_address is None:
        raise HTTPException(status_code=404, detail="Address not found")
    return db_address

@router.delete("/{address_id}", response_model=Address)
def delete_address_route(address_id: int, db: Session = Depends(get_db)):
    db_address = delete_address(db, address_id=address_id)
    if db_address is None:
        raise HTTPException(status_code=404, detail="Address not found")
    return db_address
