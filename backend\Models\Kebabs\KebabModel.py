from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON>an, Foreign<PERSON>ey, Table
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

# Association Table for Kebab and Ingredient
kebab_ingredients = Table('kebab_ingredients', Base.metadata,
    Column('kebab_id', Integer, ForeignKey('kebabs.id'), primary_key=True),
    Column('ingredient_id', Integer, ForeignKey('ingredients.id'), primary_key=True)
)

class Kebab(Base):
    __tablename__ = 'kebabs'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255))
    price = Column(Float, nullable=False)
    meat_type = Column(String(50))
    spice_level = Column(Integer, default=1) # e.g., 1-5
    image_url = Column(String(255))
    is_available = Column(Boolean, default=True)

    category_id = Column(Integer, Foreign<PERSON>ey('categories.id'))
    category = relationship("Category", back_populates="kebabs")

    menu_item_id = Column(Integer, ForeignKey('menu_items.id'))
    menu_item = relationship("MenuItem", back_populates="kebab")

    # Many-to-many relationship with Ingredient
    ingredients = relationship("Ingredient", secondary=kebab_ingredients, back_populates="kebabs")

    def __repr__(self):
        return f"<Kebab(id={self.id}, name='{self.name}')>"
