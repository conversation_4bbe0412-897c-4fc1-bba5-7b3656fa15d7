from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

class Drink(Base):
    __tablename__ = 'drinks'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255))
    price = Column(Float, nullable=False)
    size_ml = Column(Integer, nullable=False)
    is_carbonated = Column(Boolean, default=False)
    image_url = Column(String(255))
    is_available = Column(Boolean, default=True)

    category_id = Column(Integer, ForeignKey('categories.id'))
    category = relationship("Category", back_populates="drinks")

    menu_item_id = Column(Integer, ForeignKey('menu_items.id'))
    menu_item = relationship("MenuItem", back_populates="drink")

    def __repr__(self):
        return f"<Drink(id={self.id}, name='{self.name}')>"
