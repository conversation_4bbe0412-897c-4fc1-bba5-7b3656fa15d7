import React from 'react';
import { Card, Avatar } from 'flowbite-react';

const AboutUs: React.FC = () => {
  const chefs = [
    {
      name: '<PERSON><PERSON><PERSON>',
      role: 'Head Chef - Kebabs',
      image: 'https://via.placeholder.com/150',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      role: 'Chef - Salads & Appetizers',
      image: 'https://via.placeholder.com/150',
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      role: 'Chef - Doner',
      image: 'https://via.placeholder.com/150',
    },
  ];

  return (
    <div className="bg-gray-100 dark:bg-gray-900">
      <div className="container mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white sm:text-5xl">
            About Our Restaurant
          </h1>
          <p className="mt-4 text-xl text-gray-600 dark:text-gray-400">
            Serving authentic Turkish flavors with passion and tradition.
          </p>
        </div>

        <Card className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Story</h2>
          <p className="text-gray-700 dark:text-gray-400 leading-relaxed mb-4">
            Our journey began with a simple dream: to bring the rich and diverse culinary heritage of Turkey to your table. We believe in using only the freshest, locally-sourced ingredients to create our signature kebabs, doners, and salads. Every dish is a reflection of our commitment to quality and authenticity.
          </p>
          <p className="text-gray-700 dark:text-gray-400 leading-relaxed">
            From the fiery spices of our Adana Kebab to the refreshing taste of our Shepherd's Salad, we invite you to experience a taste of Turkey right here in İzmir. Our family-run restaurant is more than just a place to eat; it's a place to gather, celebrate, and create lasting memories.
          </p>
        </Card>

        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-8">Meet Our Chefs</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {chefs.map((chef, index) => (
              <Card key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  <Avatar img={chef.image} alt={chef.name} rounded size="xl" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{chef.name}</h3>
                <p className="text-gray-600 dark:text-gray-400">{chef.role}</p>
              </Card>
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-8">Find Us</h2>
          <Card className="!p-0">
            <div className="h-96">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3129.*************!2d27.13249507643243!3d38.33867907947659!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14bbdfcf31066537%3A0x21b7fd5048c61be!2s%C4%B0zmir%20Optimum%20AVM!5e0!3m2!1str!2str!4v1752399077091!5m2!1str!2str"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen={true}
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Restaurant Location"
              ></iframe>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AboutUs;
