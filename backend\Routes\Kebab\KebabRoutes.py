from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.KebabSchemas.KebabSchemas import <PERSON><PERSON><PERSON>, KebabCreate, KebabUpdate
from DataBase.DataBase import get_db
from Models.Kebabs.KebabModel import Kebab as KebabModel

router = APIRouter(
    prefix="/kebabs",
    tags=["kebabs"],
)

# CRUD Operations for Kebab
def get_kebab(db: Session, kebab_id: int) -> Optional[KebabModel]:
    return db.query(KebabModel).filter(KebabModel.id == kebab_id).first()

def get_kebabs(db: Session, skip: int = 0, limit: int = 100) -> List[KebabModel]:
    return db.query(KebabModel).offset(skip).limit(limit).all()

def create_kebab(db: Session, kebab: KebabCreate) -> KebabModel:
    db_kebab = KebabModel(**kebab.dict())
    db.add(db_kebab)
    db.commit()
    db.refresh(db_kebab)
    return db_kebab

def update_kebab(db: Session, kebab_id: int, kebab: KebabUpdate) -> Optional[KebabModel]:
    db_kebab = db.query(KebabModel).filter(KebabModel.id == kebab_id).first()
    if db_kebab:
        update_data = kebab.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_kebab, key, value)
        db.commit()
        db.refresh(db_kebab)
    return db_kebab

def delete_kebab(db: Session, kebab_id: int) -> Optional[KebabModel]:
    db_kebab = db.query(KebabModel).filter(KebabModel.id == kebab_id).first()
    if db_kebab:
        db.delete(db_kebab)
        db.commit()
    return db_kebab


@router.post("/", response_model=Kebab)
def create_kebab_route(kebab: KebabCreate, db: Session = Depends(get_db)):
    return create_kebab(db=db, kebab=kebab)

@router.get("/", response_model=List[Kebab])
def read_kebabs_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    kebabs = get_kebabs(db, skip=skip, limit=limit)
    return kebabs

@router.get("/{kebab_id}", response_model=Kebab)
def read_kebab_route(kebab_id: int, db: Session = Depends(get_db)):
    db_kebab = get_kebab(db, kebab_id=kebab_id)
    if db_kebab is None:
        raise HTTPException(status_code=404, detail="Kebab not found")
    return db_kebab

@router.put("/{kebab_id}", response_model=Kebab)
def update_kebab_route(kebab_id: int, kebab: KebabUpdate, db: Session = Depends(get_db)):
    db_kebab = update_kebab(db, kebab_id=kebab_id, kebab=kebab)
    if db_kebab is None:
        raise HTTPException(status_code=404, detail="Kebab not found")
    return db_kebab

@router.delete("/{kebab_id}", response_model=Kebab)
def delete_kebab_route(kebab_id: int, db: Session = Depends(get_db)):
    db_kebab = delete_kebab(db, kebab_id=kebab_id)
    if db_kebab is None:
        raise HTTPException(status_code=404, detail="Kebab not found")
    return db_kebab
