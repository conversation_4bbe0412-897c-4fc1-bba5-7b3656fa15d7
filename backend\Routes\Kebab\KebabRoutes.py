from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.KebabSchemas.KebabSchemas import Ke<PERSON>b, KebabCreate, KebabUpdate
from DataBase.DataBase import get_db
from Models.Kebabs.KebabModel import Kebab as KebabModel

router = APIRouter(
    prefix="/kebabs",
    tags=["kebabs"],
)


@router.post("/", response_model=Kebab)
def create_kebab_route(kebab: KebabCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Kebab])
def read_kebabs_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{kebab_id}", response_model=Kebab)
def read_kebab_route(kebab_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{kebab_id}", response_model=Kebab)
def update_kebab_route(kebab_id: int, kebab: KebabUpdate, db: Session = Depends(get_db)):
    pass
@router.delete("/{kebab_id}", response_model=Kebab)
def delete_kebab_route(kebab_id: int, db: Session = Depends(get_db)):
    pass