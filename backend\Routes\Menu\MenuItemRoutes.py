from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.MenuSchemas.MenuItemSchemas import MenuItem, MenuItemCreate, MenuItemUpdate
from DataBase.DataBase import get_db
from Models.Menu.MenuItemModel import MenuItem as MenuItemModel

router = APIRouter(
    prefix="/menu_items",
    tags=["menu_items"],
)




@router.post("/", response_model=MenuItem)
def create_menu_item_route(menu_item: MenuItemCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[MenuItem])
def read_menu_items_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{menu_item_id}", response_model=MenuItem)
def read_menu_item_route(menu_item_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{menu_item_id}", response_model=MenuItem)
def update_menu_item_route(menu_item_id: int, menu_item: MenuItemUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{menu_item_id}", response_model=MenuItem)
def delete_menu_item_route(menu_item_id: int, db: Session = Depends(get_db)):
    pass