from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.MenuSchemas.MenuItemSchemas import MenuItem, MenuItemCreate, MenuItemUpdate
from DataBase.DataBase import get_db
from Models.Menu.MenuItemModel import MenuItem as MenuItemModel

router = APIRouter(
    prefix="/menu_items",
    tags=["menu_items"],
)

# CRUD Operations for MenuItem
def get_menu_item(db: Session, menu_item_id: int) -> Optional[MenuItemModel]:
    return db.query(MenuItemModel).filter(MenuItemModel.id == menu_item_id).first()

def get_menu_items(db: Session, skip: int = 0, limit: int = 100) -> List[MenuItemModel]:
    return db.query(MenuItemModel).offset(skip).limit(limit).all()

def create_menu_item(db: Session, menu_item: MenuItemCreate) -> MenuItemModel:
    db_menu_item = MenuItemModel(**menu_item.dict())
    db.add(db_menu_item)
    db.commit()
    db.refresh(db_menu_item)
    return db_menu_item

def update_menu_item(db: Session, menu_item_id: int, menu_item: MenuItemUpdate) -> Optional[MenuItemModel]:
    db_menu_item = db.query(MenuItemModel).filter(MenuItemModel.id == menu_item_id).first()
    if db_menu_item:
        update_data = menu_item.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_menu_item, key, value)
        db.commit()
        db.refresh(db_menu_item)
    return db_menu_item

def delete_menu_item(db: Session, menu_item_id: int) -> Optional[MenuItemModel]:
    db_menu_item = db.query(MenuItemModel).filter(MenuItemModel.id == menu_item_id).first()
    if db_menu_item:
        db.delete(db_menu_item)
        db.commit()
    return db_menu_item


@router.post("/", response_model=MenuItem)
def create_menu_item_route(menu_item: MenuItemCreate, db: Session = Depends(get_db)):
    return create_menu_item(db=db, menu_item=menu_item)

@router.get("/", response_model=List[MenuItem])
def read_menu_items_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    menu_items = get_menu_items(db, skip=skip, limit=limit)
    return menu_items

@router.get("/{menu_item_id}", response_model=MenuItem)
def read_menu_item_route(menu_item_id: int, db: Session = Depends(get_db)):
    db_menu_item = get_menu_item(db, menu_item_id=menu_item_id)
    if db_menu_item is None:
        raise HTTPException(status_code=404, detail="Menu Item not found")
    return db_menu_item

@router.put("/{menu_item_id}", response_model=MenuItem)
def update_menu_item_route(menu_item_id: int, menu_item: MenuItemUpdate, db: Session = Depends(get_db)):
    db_menu_item = update_menu_item(db, menu_item_id=menu_item_id, menu_item=menu_item)
    if db_menu_item is None:
        raise HTTPException(status_code=404, detail="Menu Item not found")
    return db_menu_item

@router.delete("/{menu_item_id}", response_model=MenuItem)
def delete_menu_item_route(menu_item_id: int, db: Session = Depends(get_db)):
    db_menu_item = delete_menu_item(db, menu_item_id=menu_item_id)
    if db_menu_item is None:
        raise HTTPException(status_code=404, detail="Menu Item not found")
    return db_menu_item
