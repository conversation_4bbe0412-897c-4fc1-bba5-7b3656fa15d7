import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Home from './Pages/Home/Home';
import Header from './Components/Header/Header';
import Footer from './Components/Footer/Footer';
import Admin from './Pages/Admin/Admin';
import AuthenticatedRoute from './Components/ProtectedRoutes/AuthenticatedRoute';
import AdminRoute from './Components/ProtectedRoutes/AdminRoute';
import Profile from './Pages/Profile/Profile';
import Login from './Pages/Authentication/Login';
import Register from './Pages/Authentication/Register';
import AboutUs from './Pages/AboutUs/AboutUs';
import Orders from './Pages/Orders/Orders';
import Order from './Pages/Orders/Order';
import Menus from './Pages/Menus/Menus';
import Menu from './Pages/Menus/Menu';


function App() {
  return (
    <>
      <BrowserRouter>
        <Header />
        <Routes>

          {/* Public Routes */}
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/about" element={<AboutUs />} />
          <Route path="/menus" element={<Menus />} />
          <Route path="/menus/:id" element={<Menu />} />
          {/* Public Routes */}



          {/* Authenticated Routes */}
          <Route
            path="/profile"
            element={<AuthenticatedRoute>
              <Profile />
            </AuthenticatedRoute>}
          />
          <Route
            path="/orders"
            element={<AuthenticatedRoute>
              <Orders />
            </AuthenticatedRoute>}
          />
          <Route
            path="/orders/:id"
            element={<AuthenticatedRoute>
              <Order />
            </AuthenticatedRoute>}
          />
          {/* Authenticated Routes */}



          {/* Admin Route */}
          <Route
            path="/admin"
            element={<AdminRoute><Admin /></AdminRoute>}
          />
          {/* Admin Route */}

        </Routes>
        <Footer />
      </BrowserRouter>
    </>
  );
}

export default App;