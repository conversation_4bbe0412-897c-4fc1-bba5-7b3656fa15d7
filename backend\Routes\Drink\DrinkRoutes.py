from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.DrinkSchemas.DrinkSchemas import Drink, DrinkCreate, DrinkUpdate
from DataBase.DataBase import get_db
from Models.Drinks.DrinksModel import Drink as DrinkModel

router = APIRouter(
    prefix="/drinks",
    tags=["drinks"],
)


@router.post("/", response_model=Drink)
def create_drink_route(drink: DrinkCreate, db: Session = Depends(get_db)):
    pass

@router.get("/", response_model=List[Drink])
def read_drinks_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    pass

@router.get("/{drink_id}", response_model=Drink)
def read_drink_route(drink_id: int, db: Session = Depends(get_db)):
    pass

@router.put("/{drink_id}", response_model=Drink)
def update_drink_route(drink_id: int, drink: DrinkUpdate, db: Session = Depends(get_db)):
    pass

@router.delete("/{drink_id}", response_model=Drink)
def delete_drink_route(drink_id: int, db: Session = Depends(get_db)):
    pass