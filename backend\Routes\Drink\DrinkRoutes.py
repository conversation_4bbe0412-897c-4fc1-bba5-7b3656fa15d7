from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from Schemas.DrinkSchemas.DrinkSchemas import Drink, DrinkCreate, DrinkUpdate
from DataBase.DataBase import get_db
from Models.Drinks.DrinksModel import Drink as DrinkModel

router = APIRouter(
    prefix="/drinks",
    tags=["drinks"],
)

# CRUD Operations for Drink
def get_drink(db: Session, drink_id: int) -> Optional[DrinkModel]:
    return db.query(DrinkModel).filter(DrinkModel.id == drink_id).first()

def get_drinks(db: Session, skip: int = 0, limit: int = 100) -> List[DrinkModel]:
    return db.query(DrinkModel).offset(skip).limit(limit).all()

def create_drink(db: Session, drink: DrinkCreate) -> DrinkModel:
    db_drink = DrinkModel(**drink.dict())
    db.add(db_drink)
    db.commit()
    db.refresh(db_drink)
    return db_drink

def update_drink(db: Session, drink_id: int, drink: DrinkUpdate) -> Optional[DrinkModel]:
    db_drink = db.query(DrinkModel).filter(DrinkModel.id == drink_id).first()
    if db_drink:
        update_data = drink.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_drink, key, value)
        db.commit()
        db.refresh(db_drink)
    return db_drink

def delete_drink(db: Session, drink_id: int) -> Optional[DrinkModel]:
    db_drink = db.query(DrinkModel).filter(DrinkModel.id == drink_id).first()
    if db_drink:
        db.delete(db_drink)
        db.commit()
    return db_drink


@router.post("/", response_model=Drink)
def create_drink_route(drink: DrinkCreate, db: Session = Depends(get_db)):
    return create_drink(db=db, drink=drink)

@router.get("/", response_model=List[Drink])
def read_drinks_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    drinks = get_drinks(db, skip=skip, limit=limit)
    return drinks

@router.get("/{drink_id}", response_model=Drink)
def read_drink_route(drink_id: int, db: Session = Depends(get_db)):
    db_drink = get_drink(db, drink_id=drink_id)
    if db_drink is None:
        raise HTTPException(status_code=404, detail="Drink not found")
    return db_drink

@router.put("/{drink_id}", response_model=Drink)
def update_drink_route(drink_id: int, drink: DrinkUpdate, db: Session = Depends(get_db)):
    db_drink = update_drink(db, drink_id=drink_id, drink=drink)
    if db_drink is None:
        raise HTTPException(status_code=404, detail="Drink not found")
    return db_drink

@router.delete("/{drink_id}", response_model=Drink)
def delete_drink_route(drink_id: int, db: Session = Depends(get_db)):
    db_drink = delete_drink(db, drink_id=drink_id)
    if db_drink is None:
        raise HTTPException(status_code=404, detail="Drink not found")
    return db_drink
